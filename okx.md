## **第一轮面试**

### 1、让我介绍我简历上的项目（我之前在一家以太坊框架的电商交易平台），介绍该项目的创新点在哪，为什么想着做这个，还有最早接触区块链什么时候和机缘。

### 2、Solidity上如何在数组删除一个数？

###  3、你是如何用以太坊实现资金托管的，除了transfer还有吗？

### 4、讲讲项目中的IPFS，为什么想到用IPFS。

### 5、简单介绍一下简历上的其他项目。

### 6、有没有了解NFT。

### 7、有没有参与过公链上的其他项目。

### 8、uniswap有没有了解。

### 9、uniswap底层原理知道吗。

### 10、有没有了解以太坊版本变化。

### 11、如何对一个数组去重？还有其他实现吗？

### 12、讲一下快排是怎么一回事，时间复杂度，空间复杂度.

### 13、介绍一下个人性格。

### 14、在之前公司负责的工作范围。

### 15、之前公司部门组织结构。

### 16、面试官问了一些之前公司的业务内容。


## **第二轮面试**

### 1、自我介绍，讲一下大学毕设项目。

### 2、介绍私钥，公钥，地址，助记词的关系。

### 3、介绍比特币和以太坊的区别。

### 4、介绍POW和POS机制。

### 5、介绍POS和PBFT的关系。

### 6、介绍数字签名与验签，公私钥的作用。

### 7、介绍solidity的function的可见性。

### 8、介绍合约发布的数据结构。

### 9、有没有参与过以太坊项目/Defi/uniswap的经验。

### 10、介绍solidity函数修饰器。

### 11、介绍SHA2的过程。

### 12、介绍以太坊账户的nounce含义。


## **算法题口述** 

### 1、介绍逆排链表。

### 2、介绍二叉排序树。

### 3、介绍二叉排序平衡树。

### 4、介绍红黑树。

### 5、介绍最小生成树（两个算法）。

### 6、介绍Dijkstra算法。


## **思维题**

### 1、有100桶酒，其中一桶酒里有毒，一只实验老鼠喝了酒之后一小时有结果，问一小时之内最少需要多少只老鼠可以试出毒酒？


## **第三轮面试**

### 1、自我介绍一下。

### 2、简单介绍毕设。

### 3、了解Solidity语言多长时间。

### 4、之前工作遇到的困难。

### 5、为什么毕设要挑一个完全不熟悉的方向。

### 6、问我这段时间求职有拿过其他offer吗。

### 7、问我的期望薪资。

### 8、你对Web3行业发展怎么看。


## **Offer后沟通**

### 1、聊了聊职业规划，和行业的一些政策。

### 2、问我职业发展主要考虑什么。

### 3、对薪资的看法。

### 4、谈对Web3、尤其公链的理解。

### 5、问我C++拷贝构造函数与go语言。

### 6、问我想参与上层应用开发，还是底层区块链开发。

### 7、介绍分布式办公模式。