## **第一轮面试**

### 1、让我介绍我简历上的项目（我之前在一家以太坊框架的电商交易平台），介绍该项目的创新点在哪，为什么想着做这个，还有最早接触区块链什么时候和机缘。

**答案：**

**项目背景：**
基于以太坊的去中心化电商交易平台，旨在解决传统电商的信任问题和中心化风险。

**核心创新点：**

1. **去中心化托管系统**
   - 使用智能合约替代传统第三方支付
   - 买家资金自动托管，确认收货后释放给卖家
   - 争议解决通过DAO投票机制

2. **信誉系统上链**
   - 所有交易记录和评价不可篡改
   - 基于区块链的信誉积分系统
   - 防止刷单和虚假评价

3. **供应链透明化**
   - 商品从生产到销售全程可追溯
   - 结合IPFS存储商品信息和物流数据
   - 消费者可验证商品真实性

**技术架构：**
```
前端(React) → Web3.js → 以太坊网络
                    ↓
智能合约层：交易合约、托管合约、信誉合约
                    ↓
存储层：IPFS(商品信息) + 以太坊(交易数据)
```

**为什么选择这个方向：**
- 传统电商存在信任成本高、数据垄断等问题
- 区块链的去中心化和不可篡改特性能很好解决这些痛点
- 看好Web3电商的发展前景

**接触区块链的机缘：**
- 2017年比特币牛市开始关注加密货币
- 2018年开始学习以太坊和智能合约开发
- 2019年参与DeFi项目，深入理解区块链应用价值
- 认为区块链不仅是投机工具，更是改变商业模式的技术革命

### 2、Solidity上如何在数组删除一个数？

**答案：**

在 Solidity 中删除数组元素有多种方法：

**方法一：使用 delete 关键字**
```solidity
uint[] public numbers = [1, 2, 3, 4, 5];

function deleteElement(uint index) public {
    delete numbers[index]; // 将该位置设为默认值0，但不改变数组长度
}
```

**方法二：交换删除法（Swap and Pop）**
```solidity
function removeElement(uint index) public {
    require(index < numbers.length, "Index out of bounds");

    // 将要删除的元素与最后一个元素交换
    numbers[index] = numbers[numbers.length - 1];
    // 删除最后一个元素
    numbers.pop();
}
```

**方法三：移位删除法**
```solidity
function removeElementShift(uint index) public {
    require(index < numbers.length, "Index out of bounds");

    // 将后面的元素向前移动
    for (uint i = index; i < numbers.length - 1; i++) {
        numbers[i] = numbers[i + 1];
    }
    numbers.pop();
}
```

**最佳实践：**
- 交换删除法：O(1) 时间复杂度，但不保持顺序
- 移位删除法：O(n) 时间复杂度，但保持顺序
- delete 关键字：适用于映射或需要保持数组长度的场景

###  3、你是如何用以太坊实现资金托管的，除了transfer还有吗？

**答案：**

以太坊资金托管可以通过多种方式实现，除了 `transfer` 还有其他方法：

**1. 使用 call 方法**
```solidity
contract Escrow {
    address payable public buyer;
    address payable public seller;
    address public arbiter;

    function releaseFunds() public {
        require(msg.sender == arbiter, "Only arbiter can release");

        // 使用 call 方法转账，推荐方式
        (bool success, ) = seller.call{value: address(this).balance}("");
        require(success, "Transfer failed");
    }
}
```

**2. 使用 send 方法**
```solidity
function releaseFundsWithSend() public {
    require(msg.sender == arbiter, "Only arbiter can release");

    bool success = seller.send(address(this).balance);
    require(success, "Transfer failed");
}
```

**3. 完整的托管合约示例**
```solidity
contract SimpleEscrow {
    enum State { AWAITING_PAYMENT, AWAITING_DELIVERY, COMPLETE, REFUNDED }

    State public currentState;
    address payable public buyer;
    address payable public seller;
    address public arbiter;
    uint public amount;

    constructor(address payable _seller, address _arbiter) {
        buyer = payable(msg.sender);
        seller = _seller;
        arbiter = _arbiter;
    }

    function deposit() external payable onlyBuyer inState(State.AWAITING_PAYMENT) {
        amount = msg.value;
        currentState = State.AWAITING_DELIVERY;
    }

    function confirmDelivery() external onlyBuyer inState(State.AWAITING_DELIVERY) {
        seller.transfer(amount);
        currentState = State.COMPLETE;
    }

    function refund() external onlyArbiter inState(State.AWAITING_DELIVERY) {
        buyer.transfer(amount);
        currentState = State.REFUNDED;
    }
}
```

**转账方法对比：**
- `transfer`: 2300 gas 限制，失败时回滚，已不推荐
- `send`: 2300 gas 限制，返回 bool 值，需手动检查
- `call`: 无 gas 限制，最灵活，当前推荐方式

### 4、讲讲项目中的IPFS，为什么想到用IPFS。

**答案：**

**IPFS（InterPlanetary File System）简介：**
IPFS 是一个分布式的点对点文件系统，旨在创建一个持久化、分布式存储和共享文件的网络传输协议。

**在区块链项目中使用 IPFS 的原因：**

1. **解决区块链存储限制**
   - 区块链存储成本高昂（以太坊每字节约 20,000 gas）
   - 不适合存储大文件如图片、视频、文档

2. **去中心化存储**
   - 避免单点故障
   - 数据不依赖于中心化服务器
   - 符合区块链去中心化理念

3. **内容寻址**
   - 通过内容哈希（CID）访问文件
   - 确保数据完整性和不可篡改性
   - 相同内容只存储一份，节省空间

**实际应用场景：**

```solidity
contract NFTWithIPFS {
    struct TokenMetadata {
        string name;
        string description;
        string ipfsHash; // 存储在IPFS上的元数据哈希
    }

    mapping(uint256 => TokenMetadata) public tokenMetadata;

    function mintNFT(
        address to,
        uint256 tokenId,
        string memory ipfsHash
    ) public {
        tokenMetadata[tokenId] = TokenMetadata({
            name: "My NFT",
            description: "Stored on IPFS",
            ipfsHash: ipfsHash
        });
        _mint(to, tokenId);
    }

    function tokenURI(uint256 tokenId) public view returns (string memory) {
        return string(abi.encodePacked("ipfs://", tokenMetadata[tokenId].ipfsHash));
    }
}
```

**技术优势：**
- 版本控制：支持文件历史版本
- 重复数据删除：相同内容共享存储
- 离线访问：支持本地缓存
- 带宽优化：就近节点获取数据

### 5、简单介绍一下简历上的其他项目。

### 6、有没有了解NFT。

**答案：**

**NFT（Non-Fungible Token）非同质化代币**

**核心概念：**
- 基于区块链的唯一数字资产证书
- 每个NFT都有独特的标识符，不可互换
- 主要基于ERC-721和ERC-1155标准

**技术实现：**
```solidity
contract MyNFT is ERC721 {
    uint256 private _tokenIds;

    constructor() ERC721("MyNFT", "MNFT") {}

    function mintNFT(address recipient, string memory tokenURI)
        public returns (uint256) {
        _tokenIds++;
        uint256 newItemId = _tokenIds;
        _mint(recipient, newItemId);
        _setTokenURI(newItemId, tokenURI);
        return newItemId;
    }
}
```

**应用场景：**
- 数字艺术品和收藏品
- 游戏道具和虚拟资产
- 域名和身份认证
- 音乐和视频版权
- 房地产和实物资产代币化

**市场现状：**
- OpenSea、Blur等交易平台
- 艺术家和创作者新的变现方式
- 品牌营销和社区建设工具

### 7、有没有参与过公链上的其他项目。

**答案：**

**参与的公链项目经验：**

**1. DeFi协议开发**
- 参与流动性挖矿合约开发
- 实现自动化做市商（AMM）机制
- 集成多个DeFi协议进行收益优化

**2. DAO治理系统**
- 开发基于代币的投票机制
- 实现提案创建和执行流程
- 集成多签钱包进行资金管理

**3. 跨链桥项目**
- 参与以太坊到BSC的资产跨链
- 实现锁定-铸造机制
- 处理跨链交易验证和安全问题

**技术栈：**
- Solidity智能合约开发
- Web3.js/Ethers.js前端集成
- Hardhat/Truffle开发框架
- IPFS去中心化存储

### 8、uniswap有没有了解。

**答案：**

**Uniswap深度了解：**

**基本概念：**
- 去中心化交易所（DEX）的领导者
- 自动化做市商（AMM）模式
- 无需订单簿，通过流动性池进行交易

**版本演进：**
- **V1**: 概念验证，ETH-ERC20交易对
- **V2**: 支持任意ERC20交易对，价格预言机
- **V3**: 集中流动性，多级手续费，NFT LP代币

**核心机制：**
```solidity
// V2核心公式
x * y = k
// 其中x和y是两种代币的储备量，k是常数
```

**实际使用经验：**
- 作为流动性提供者参与挖矿
- 开发套利机器人捕获价差
- 集成Uniswap SDK进行代币交换
- 分析无常损失和收益策略

### 9、uniswap底层原理知道吗。

**答案：**

**Uniswap核心原理：自动化做市商（AMM）**

**1. 恒定乘积公式（x * y = k）**
```
x * y = k (常数)
```
- x: 代币A的数量
- y: 代币B的数量
- k: 恒定常数

**2. 价格发现机制**
```solidity
contract UniswapV2Pair {
    uint112 private reserve0;
    uint112 private reserve1;

    function getAmountOut(uint amountIn, uint reserveIn, uint reserveOut)
        public pure returns (uint amountOut) {
        require(amountIn > 0, 'INSUFFICIENT_INPUT_AMOUNT');
        require(reserveIn > 0 && reserveOut > 0, 'INSUFFICIENT_LIQUIDITY');

        uint amountInWithFee = amountIn * 997; // 0.3%手续费
        uint numerator = amountInWithFee * reserveOut;
        uint denominator = reserveIn * 1000 + amountInWithFee;
        amountOut = numerator / denominator;
    }
}
```

**3. 流动性提供机制**
```solidity
function addLiquidity(
    address tokenA,
    address tokenB,
    uint amountADesired,
    uint amountBDesired
) external returns (uint amountA, uint amountB, uint liquidity) {

    // 计算最优比例
    (uint reserveA, uint reserveB) = getReserves(tokenA, tokenB);

    if (reserveA == 0 && reserveB == 0) {
        (amountA, amountB) = (amountADesired, amountBDesired);
    } else {
        uint amountBOptimal = quote(amountADesired, reserveA, reserveB);
        if (amountBOptimal <= amountBDesired) {
            (amountA, amountB) = (amountADesired, amountBOptimal);
        } else {
            uint amountAOptimal = quote(amountBDesired, reserveB, reserveA);
            (amountA, amountB) = (amountAOptimal, amountBDesired);
        }
    }

    // 铸造LP代币
    liquidity = Math.min(
        amountA * totalSupply / reserveA,
        amountB * totalSupply / reserveB
    );
}
```

**4. 交易滑点计算**
```
滑点 = (预期价格 - 实际价格) / 预期价格 * 100%

实际获得代币数量 = (输入数量 * 997 * 输出储备) / (输入储备 * 1000 + 输入数量 * 997)
```

**5. Uniswap V2 vs V3 主要区别**

**V2特点：**
- 全价格区间流动性
- 简单的50/50资产配比
- 固定0.3%手续费

**V3特点：**
```solidity
struct Position {
    uint128 liquidity;
    int24 tickLower;    // 价格下限
    int24 tickUpper;    // 价格上限
    uint256 feeGrowthInside0LastX128;
    uint256 feeGrowthInside1LastX128;
}
```
- 集中流动性（Concentrated Liquidity）
- 多级手续费（0.05%, 0.3%, 1%）
- 更高的资本效率

**6. 套利机制**
当Uniswap价格偏离市场价格时，套利者会：
1. 在价格较低的平台买入
2. 在价格较高的平台卖出
3. 获得价差收益
4. 使价格趋于一致

**优势：**
- 无需订单簿
- 永远有流动性
- 去中心化运行
- 抗审查性

**局限性：**
- 无常损失（Impermanent Loss）
- 大额交易滑点较高
- Gas费用较高

### 10、有没有了解以太坊版本变化。

### 11、如何对一个数组去重？还有其他实现吗？

**答案：**

**方法一：使用 Set（哈希表）- 最常用**
```go
func removeDuplicates(nums []int) []int {
    seen := make(map[int]bool)
    result := []int{}

    for _, num := range nums {
        if !seen[num] {
            seen[num] = true
            result = append(result, num)
        }
    }
    return result
}
// 时间复杂度：O(n)，空间复杂度：O(n)
```

**方法二：双指针法（适用于已排序数组）**
```go
func removeDuplicatesSorted(nums []int) []int {
    if len(nums) <= 1 {
        return nums
    }

    slow := 0
    for fast := 1; fast < len(nums); fast++ {
        if nums[fast] != nums[slow] {
            slow++
            nums[slow] = nums[fast]
        }
    }
    return nums[:slow+1]
}
// 时间复杂度：O(n)，空间复杂度：O(1)
```

**方法三：先排序再去重**
```go
import "sort"

func removeDuplicatesWithSort(nums []int) []int {
    sort.Ints(nums)

    if len(nums) <= 1 {
        return nums
    }

    result := []int{nums[0]}
    for i := 1; i < len(nums); i++ {
        if nums[i] != nums[i-1] {
            result = append(result, nums[i])
        }
    }
    return result
}
// 时间复杂度：O(n log n)，空间复杂度：O(1)
```

**方法四：位图法（适用于数值范围较小的情况）**
```go
func removeDuplicatesBitmap(nums []int) []int {
    const maxVal = 1000 // 假设数值范围在0-1000
    bitmap := make([]bool, maxVal+1)
    result := []int{}

    for _, num := range nums {
        if num >= 0 && num <= maxVal && !bitmap[num] {
            bitmap[num] = true
            result = append(result, num)
        }
    }
    return result
}
// 时间复杂度：O(n)，空间复杂度：O(k)，k为数值范围
```

**各方法对比：**
- Set方法：通用性最好，适用于任何类型
- 双指针：空间效率最高，但需要预排序
- 排序法：在某些场景下可以复用排序结果
- 位图法：在特定条件下效率最高

### 12、讲一下快排是怎么一回事，时间复杂度，空间复杂度.

**答案：**

**快速排序原理：**
快速排序是一种分治算法，通过选择一个"基准"元素，将数组分为两部分：小于基准的元素和大于基准的元素，然后递归地对这两部分进行排序。

**算法步骤：**
1. 选择基准元素（pivot）
2. 分区操作：重新排列数组，使得小于基准的元素在左边，大于基准的在右边
3. 递归地对左右两个子数组进行快速排序

**Go语言实现：**
```go
func quickSort(arr []int, low, high int) {
    if low < high {
        // 分区操作，返回基准元素的正确位置
        pi := partition(arr, low, high)

        // 递归排序基准元素左边的子数组
        quickSort(arr, low, pi-1)
        // 递归排序基准元素右边的子数组
        quickSort(arr, pi+1, high)
    }
}

func partition(arr []int, low, high int) int {
    // 选择最后一个元素作为基准
    pivot := arr[high]
    i := low - 1 // 较小元素的索引

    for j := low; j < high; j++ {
        // 如果当前元素小于或等于基准
        if arr[j] <= pivot {
            i++
            arr[i], arr[j] = arr[j], arr[i] // 交换元素
        }
    }
    arr[i+1], arr[high] = arr[high], arr[i+1] // 将基准放到正确位置
    return i + 1
}
```

**复杂度分析：**

**时间复杂度：**
- 最好情况：O(n log n) - 每次分区都能将数组平均分割
- 平均情况：O(n log n) - 随机选择基准的期望性能
- 最坏情况：O(n²) - 每次选择的基准都是最大或最小值

**空间复杂度：**
- 最好情况：O(log n) - 递归调用栈的深度
- 最坏情况：O(n) - 当数组已经有序时，递归深度为n

**优化策略：**
1. **三数取中法选择基准**
2. **小数组使用插入排序**
3. **尾递归优化**
4. **随机化基准选择**

**快排的优势：**
- 原地排序，空间效率高
- 平均性能优秀
- 缓存友好，实际运行速度快

### 13、介绍一下个人性格。

### 14、在之前公司负责的工作范围。

### 15、之前公司部门组织结构。

### 16、面试官问了一些之前公司的业务内容。


## **第二轮面试**

### 1、自我介绍，讲一下大学毕设项目。

### 2、介绍私钥，公钥，地址，助记词的关系。

**答案：**

**关系链条：助记词 → 私钥 → 公钥 → 地址**

**1. 助记词（Mnemonic Phrase）**
- 12或24个英文单词组成的人类可读的种子短语
- 基于BIP39标准生成
- 用于恢复和备份钱包
- 示例：`abandon ability able about above absent absorb abstract absurd abuse access accident`

**2. 私钥（Private Key）**
- 256位随机数，通常表示为64位十六进制字符串
- 从助记词通过PBKDF2算法派生而来
- 用于数字签名，证明资产所有权
- 必须严格保密，泄露即失去资产控制权
- 示例：`0x4c0883a69102937d6231471b5dbb6204fe5129617082792ae468d01a3f362318`

**3. 公钥（Public Key）**
- 通过椭圆曲线数字签名算法（ECDSA）从私钥计算得出
- 65字节（未压缩）或33字节（压缩）
- 用于验证数字签名
- 可以公开分享，不会泄露私钥信息

**4. 地址（Address）**
- 从公钥通过Keccak-256哈希算法计算得出
- 以太坊地址：取公钥哈希的后20字节，加上0x前缀
- 用于接收和发送交易
- 示例：`******************************************`

**生成过程代码示例：**
```go
import (
    "crypto/ecdsa"
    "github.com/ethereum/go-ethereum/crypto"
    "github.com/ethereum/go-ethereum/common"
)

// 从私钥生成公钥和地址
func generateKeyPair(privateKeyHex string) {
    // 1. 私钥
    privateKey, _ := crypto.HexToECDSA(privateKeyHex)

    // 2. 公钥
    publicKey := privateKey.Public().(*ecdsa.PublicKey)

    // 3. 地址
    address := crypto.PubkeyToAddress(*publicKey)

    fmt.Printf("私钥: %x\n", privateKey.D)
    fmt.Printf("地址: %s\n", address.Hex())
}
```

**安全层级：**
- 助记词：最高安全级别，可恢复所有密钥
- 私钥：高安全级别，控制单个账户
- 公钥：中等安全级别，可验证签名
- 地址：公开信息，无安全风险

### 3、介绍比特币和以太坊的区别。

**答案：**

**核心定位差异：**
- **比特币**：数字黄金，价值存储和点对点电子现金系统
- **以太坊**：世界计算机，去中心化应用平台和智能合约平台

**技术架构对比：**

| 特性 | 比特币 | 以太坊 |
|------|--------|--------|
| **共识机制** | POW（工作量证明） | POW → POS（权益证明，2022年合并后） |
| **区块时间** | ~10分钟 | ~12-15秒 |
| **区块大小** | 1MB | 动态调整（Gas Limit） |
| **编程语言** | Script（有限） | Solidity、Vyper等（图灵完备） |
| **虚拟机** | 无 | EVM（以太坊虚拟机） |
| **账户模型** | UTXO模型 | 账户余额模型 |

**功能对比：**

**比特币特点：**
```
- 简单的转账功能
- 高度安全和去中心化
- 有限的脚本功能
- 通胀控制（2100万枚上限）
- 能耗较高（POW挖矿）
```

**以太坊特点：**
```
- 智能合约支持
- DApp生态系统
- DeFi、NFT、DAO等应用
- 更快的交易确认
- 更复杂的功能但相对复杂度更高
```

**代码示例对比：**

**比特币脚本（简单转账）：**
```
OP_DUP OP_HASH160 <pubKeyHash> OP_EQUALVERIFY OP_CHECKSIG
```

**以太坊智能合约（ERC20代币）：**
```solidity
contract ERC20Token {
    mapping(address => uint256) public balanceOf;

    function transfer(address to, uint256 amount) public returns (bool) {
        require(balanceOf[msg.sender] >= amount, "Insufficient balance");
        balanceOf[msg.sender] -= amount;
        balanceOf[to] += amount;
        emit Transfer(msg.sender, to, amount);
        return true;
    }
}
```

**生态系统差异：**
- **比特币**：主要用于价值存储、支付、对冲通胀
- **以太坊**：DeFi协议、NFT市场、去中心化交易所、DAO治理

**发展方向：**
- **比特币**：闪电网络、Taproot升级、侧链扩展
- **以太坊**：Layer2扩容、分片技术、POS优化

### 4、介绍POW和POS机制。

**答案：**

**POW（Proof of Work - 工作量证明）**

**原理：**
矿工通过计算密集型的数学难题来竞争记账权，第一个解决难题的矿工获得区块奖励。

**工作流程：**
1. 收集待确认交易
2. 构造区块头（包含前一区块哈希、交易根哈希、时间戳、难度目标）
3. 不断尝试不同的nonce值，计算区块哈希
4. 找到满足难度要求的哈希值（前导零的数量）
5. 广播区块，其他节点验证并接受

**代码示例（简化版）：**
```go
func mineBlock(block *Block, difficulty int) {
    target := strings.Repeat("0", difficulty)

    for {
        hash := calculateHash(block)
        if strings.HasPrefix(hash, target) {
            fmt.Printf("Block mined: %s\n", hash)
            block.Hash = hash
            break
        }
        block.Nonce++
    }
}
```

**POS（Proof of Stake - 权益证明）**

**原理：**
验证者根据其持有的代币数量（权益）来获得记账权，权益越大，被选中的概率越高。

**工作流程：**
1. 验证者质押一定数量的代币
2. 根据权益比例随机选择验证者
3. 被选中的验证者提议新区块
4. 其他验证者对区块进行投票验证
5. 达到共识后区块被确认

**以太坊2.0 POS机制：**
```solidity
// 简化的验证者质押合约
contract ValidatorStaking {
    uint256 public constant MIN_STAKE = 32 ether;

    struct Validator {
        address owner;
        uint256 stake;
        bool active;
    }

    mapping(address => Validator) public validators;

    function stake() external payable {
        require(msg.value >= MIN_STAKE, "Insufficient stake");
        validators[msg.sender] = Validator({
            owner: msg.sender,
            stake: msg.value,
            active: true
        });
    }

    function slash(address validator, uint256 amount) external {
        // 惩罚恶意验证者
        validators[validator].stake -= amount;
    }
}
```

**对比分析：**

| 特性 | POW | POS |
|------|-----|-----|
| **能耗** | 极高 | 极低（节能99%+） |
| **硬件要求** | 专业矿机 | 普通计算机 |
| **准入门槛** | 高（设备投资） | 中等（代币质押） |
| **去中心化程度** | 高 | 中高 |
| **安全性** | 经过验证 | 理论安全 |
| **可扩展性** | 低 | 相对较高 |
| **最终确定性** | 概率性 | 确定性 |

**安全机制：**
- **POW**：51%算力攻击成本极高
- **POS**：恶意行为会被罚没质押资产（Slashing）

**发展趋势：**
- POW：比特币等继续使用，注重安全性
- POS：以太坊等新公链采用，注重效率和环保

### 5、介绍POS和PBFT的关系。

**答案：**

**POS和PBFT的关系与区别**

**PBFT（Practical Byzantine Fault Tolerance）**
- **定位**：经典的拜占庭容错算法
- **适用场景**：联盟链、私有链（节点数量有限且相对可信）
- **容错能力**：可容忍不超过1/3的恶意节点
- **确定性**：提供即时最终确定性

**POS（Proof of Stake）**
- **定位**：区块链共识机制
- **适用场景**：公有链（节点数量庞大且不可信）
- **容错能力**：通过经济激励和惩罚机制保证安全
- **确定性**：概率性最终确定性

**两者关系：**

**1. 互补关系**
```
POS + PBFT = 更强的共识系统
```
许多现代区块链将两者结合：
- POS负责验证者选择和经济激励
- PBFT负责具体的共识过程

**2. 以太坊2.0的Casper FFG**
```solidity
// 简化的Casper FFG概念
contract CasperFFG {
    struct Checkpoint {
        uint256 epoch;
        bytes32 root;
        bool justified;
        bool finalized;
    }

    mapping(uint256 => Checkpoint) public checkpoints;
    mapping(address => uint256) public validatorStakes;

    function vote(
        uint256 sourceEpoch,
        uint256 targetEpoch,
        bytes32 targetRoot
    ) external {
        require(validatorStakes[msg.sender] > 0, "Not a validator");

        // PBFT风格的投票机制
        // 需要2/3的验证者投票才能确认
        processVote(msg.sender, sourceEpoch, targetEpoch, targetRoot);
    }
}
```

**3. Tendermint共识（Cosmos生态）**
```
Tendermint = POS + PBFT的经典结合
```
- 使用POS选择验证者集合
- 使用改进的PBFT进行区块确认
- 提供即时最终确定性

**技术对比：**

| 特性 | PBFT | POS | POS+PBFT |
|------|------|-----|----------|
| **节点数量** | 有限(<100) | 无限制 | 大量但有选择 |
| **网络类型** | 联盟链 | 公有链 | 公有链 |
| **最终确定性** | 即时 | 概率性 | 即时 |
| **通信复杂度** | O(n²) | O(n) | O(n²)在验证者间 |
| **分叉处理** | 无分叉 | 可能分叉 | 无分叉 |

**实际应用案例：**

**1. Ethereum 2.0**
- POS：验证者质押ETH获得记账权
- PBFT变种：Casper FFG提供最终确定性

**2. Cosmos Hub**
- Tendermint共识 = POS + PBFT
- 验证者通过ATOM代币质押选出
- 使用PBFT确保即时最终确定性

**3. Polkadot**
- GRANDPA最终确定性工具 = POS + PBFT思想
- 验证者通过DOT质押选择
- 使用GRANDPA算法确保最终确定性

**优势结合：**
- POS的经济激励 + PBFT的安全保证
- 可扩展性 + 即时最终确定性
- 去中心化 + 高效共识

**总结：**
POS和PBFT不是竞争关系，而是互补关系。现代区块链系统往往将两者结合，用POS解决验证者选择和经济激励问题，用PBFT类算法解决具体的共识和最终确定性问题。

### 6、介绍数字签名与验签，公私钥的作用。

### 7、介绍solidity的function的可见性。

**答案：**

Solidity中函数有四种可见性修饰符，控制函数的访问权限：

**1. public（公开）**
```solidity
contract Example {
    uint256 public value; // 自动生成getter函数

    function publicFunction() public returns (uint256) {
        return value;
    }
}
```
- 可以被任何地方调用（内部、外部、继承合约）
- 会自动生成外部接口
- Gas消耗相对较高

**2. external（外部）**
```solidity
function externalFunction(uint256 _value) external {
    // 只能从合约外部调用
    // 内部调用需要使用 this.externalFunction()
}

function callExternal() public {
    // 错误：不能直接调用
    // externalFunction(100);

    // 正确：通过this调用
    this.externalFunction(100);
}
```
- 只能从合约外部调用
- Gas效率比public高（参数直接从calldata读取）
- 适用于只被外部调用的函数

**3. internal（内部）**
```solidity
contract Parent {
    function internalFunction() internal pure returns (string memory) {
        return "Internal function";
    }
}

contract Child is Parent {
    function useInternal() public pure returns (string memory) {
        return internalFunction(); // 可以调用父合约的internal函数
    }
}
```
- 只能在当前合约内部和继承合约中调用
- 不能从外部直接访问
- 默认的可见性（如果不指定）

**4. private（私有）**
```solidity
contract Example {
    uint256 private secretValue;

    function privateFunction() private pure returns (uint256) {
        return 42;
    }

    function usePrivate() public pure returns (uint256) {
        return privateFunction(); // 只能在当前合约内调用
    }
}

contract Child is Example {
    function tryAccess() public pure returns (uint256) {
        // 错误：无法访问父合约的private函数
        // return privateFunction();
        return 0;
    }
}
```
- 只能在当前合约内部调用
- 继承合约也无法访问
- 最严格的访问控制

**可见性对比表：**

| 可见性 | 内部调用 | 外部调用 | 继承合约 | Gas效率 |
|--------|----------|----------|----------|---------|
| public | ✅ | ✅ | ✅ | 低 |
| external | ❌* | ✅ | ✅ | 高 |
| internal | ✅ | ❌ | ✅ | 中 |
| private | ✅ | ❌ | ❌ | 中 |

*需要通过this.function()调用

**最佳实践：**
1. 优先使用最严格的可见性
2. 只被外部调用的函数使用external
3. 工具函数使用internal或private
4. 状态变量的getter函数考虑使用external而非public

### 8、介绍合约发布的数据结构。

### 9、有没有参与过以太坊项目/Defi/uniswap的经验。

### 10、介绍solidity函数修饰器。

### 11、介绍SHA2的过程。

### 12、介绍以太坊账户的nounce含义。


## **算法题口述** 

### 1、介绍逆排链表。

**答案：**

**链表反转（逆排链表）**是将单链表的指向关系完全颠倒的操作。

**算法思路：**
使用三个指针：prev（前驱）、curr（当前）、next（后继），逐个反转节点的指向。

**Go语言实现：**

```go
type ListNode struct {
    Val  int
    Next *ListNode
}

// 迭代方法 - 推荐
func reverseList(head *ListNode) *ListNode {
    var prev *ListNode
    curr := head

    for curr != nil {
        next := curr.Next    // 保存下一个节点
        curr.Next = prev     // 反转当前节点指向
        prev = curr          // 移动prev指针
        curr = next          // 移动curr指针
    }

    return prev // prev成为新的头节点
}

// 递归方法
func reverseListRecursive(head *ListNode) *ListNode {
    // 基础情况
    if head == nil || head.Next == nil {
        return head
    }

    // 递归反转后面的链表
    newHead := reverseListRecursive(head.Next)

    // 反转当前节点
    head.Next.Next = head
    head.Next = nil

    return newHead
}

// 反转链表的指定区间 [left, right]
func reverseBetween(head *ListNode, left, right int) *ListNode {
    if left == right {
        return head
    }

    dummy := &ListNode{Next: head}
    prev := dummy

    // 找到left前一个节点
    for i := 0; i < left-1; i++ {
        prev = prev.Next
    }

    // 反转[left, right]区间
    curr := prev.Next
    for i := 0; i < right-left; i++ {
        next := curr.Next
        curr.Next = next.Next
        next.Next = prev.Next
        prev.Next = next
    }

    return dummy.Next
}
```

**复杂度分析：**
- 时间复杂度：O(n)，需要遍历整个链表
- 空间复杂度：
  - 迭代方法：O(1)，只使用常数额外空间
  - 递归方法：O(n)，递归调用栈深度

**应用场景：**
1. 回文链表检测
2. 链表部分反转
3. 链表重排序
4. 实现栈和队列的相互转换

### 2、介绍二叉排序树。

**答案：**

**二叉排序树（Binary Search Tree, BST）**是一种特殊的二叉树，满足以下性质：

**定义特性：**
1. 左子树所有节点值 < 根节点值
2. 右子树所有节点值 > 根节点值
3. 左右子树也都是二叉排序树
4. 中序遍历得到有序序列

**Go语言实现：**

```go
type TreeNode struct {
    Val   int
    Left  *TreeNode
    Right *TreeNode
}

type BST struct {
    Root *TreeNode
}

// 插入操作
func (bst *BST) Insert(val int) {
    bst.Root = insertNode(bst.Root, val)
}

func insertNode(root *TreeNode, val int) *TreeNode {
    if root == nil {
        return &TreeNode{Val: val}
    }

    if val < root.Val {
        root.Left = insertNode(root.Left, val)
    } else if val > root.Val {
        root.Right = insertNode(root.Right, val)
    }
    // 相等时不插入（避免重复）

    return root
}

// 查找操作
func (bst *BST) Search(val int) bool {
    return searchNode(bst.Root, val)
}

func searchNode(root *TreeNode, val int) bool {
    if root == nil {
        return false
    }

    if val == root.Val {
        return true
    } else if val < root.Val {
        return searchNode(root.Left, val)
    } else {
        return searchNode(root.Right, val)
    }
}

// 删除操作
func (bst *BST) Delete(val int) {
    bst.Root = deleteNode(bst.Root, val)
}

func deleteNode(root *TreeNode, val int) *TreeNode {
    if root == nil {
        return nil
    }

    if val < root.Val {
        root.Left = deleteNode(root.Left, val)
    } else if val > root.Val {
        root.Right = deleteNode(root.Right, val)
    } else {
        // 找到要删除的节点
        if root.Left == nil {
            return root.Right
        } else if root.Right == nil {
            return root.Left
        }

        // 有两个子节点：找到右子树的最小值
        minNode := findMin(root.Right)
        root.Val = minNode.Val
        root.Right = deleteNode(root.Right, minNode.Val)
    }

    return root
}

func findMin(root *TreeNode) *TreeNode {
    for root.Left != nil {
        root = root.Left
    }
    return root
}

// 中序遍历（得到有序序列）
func inorderTraversal(root *TreeNode) []int {
    var result []int
    if root != nil {
        result = append(result, inorderTraversal(root.Left)...)
        result = append(result, root.Val)
        result = append(result, inorderTraversal(root.Right)...)
    }
    return result
}
```

**复杂度分析：**

| 操作 | 平均情况 | 最坏情况 | 最好情况 |
|------|----------|----------|----------|
| 查找 | O(log n) | O(n) | O(1) |
| 插入 | O(log n) | O(n) | O(1) |
| 删除 | O(log n) | O(n) | O(1) |

**优缺点：**

**优点：**
- 查找效率高（平均O(log n)）
- 中序遍历得到有序序列
- 动态维护有序数据
- 支持范围查询

**缺点：**
- 可能退化为链表（最坏情况O(n)）
- 不保证平衡性
- 删除操作相对复杂

**应用场景：**
1. 数据库索引
2. 表达式解析
3. 文件系统目录结构
4. 优先队列实现

**改进版本：**
- AVL树：严格平衡
- 红黑树：近似平衡，插入删除效率更高
- B树/B+树：适用于磁盘存储

### 3、介绍二叉排序平衡树。

### 4、介绍红黑树。

### 5、介绍最小生成树（两个算法）。

### 6、介绍Dijkstra算法。


## **思维题**

### 1、有100桶酒，其中一桶酒里有毒，一只实验老鼠喝了酒之后一小时有结果，问一小时之内最少需要多少只老鼠可以试出毒酒？

**答案：**

**解题思路：二进制编码**

这是一个经典的信息论问题，需要用二进制思维来解决。

**核心思想：**
- 每只老鼠有两种状态：死亡(1) 或 存活(0)
- n只老鼠可以表示2^n种不同的状态
- 需要找到最小的n，使得2^n ≥ 100

**计算过程：**
```
2^6 = 64 < 100
2^7 = 128 > 100
```
因此需要**7只老鼠**。

**具体操作方法：**

1. **给100桶酒编号**：0-99（用二进制表示）
2. **给7只老鼠编号**：分别代表二进制的第0位到第6位
3. **喂酒规则**：如果酒桶编号的二进制表示中某一位为1，就让对应的老鼠喝这桶酒

**示例说明：**
```
酒桶编号 | 二进制  | 喝酒的老鼠编号
---------|---------|---------------
0号酒桶  | 0000000 | 无老鼠喝
1号酒桶  | 0000001 | 0号老鼠
2号酒桶  | 0000010 | 1号老鼠
3号酒桶  | 0000011 | 0号和1号老鼠
...      | ...     | ...
50号酒桶 | 0110010 | 1号、4号、5号老鼠
...      | ...     | ...
99号酒桶 | 1100011 | 0号、1号、5号、6号老鼠
```

**结果判断：**
一小时后，根据老鼠的死亡情况，将死亡老鼠对应的位设为1，存活老鼠对应的位设为0，组成的二进制数就是有毒酒桶的编号。

**Go语言实现：**
```go
func findPoisonBottle(deadMice []int) int {
    result := 0
    for _, mouseId := range deadMice {
        result |= (1 << mouseId) // 将对应位设为1
    }
    return result
}

// 示例：如果0号、2号、4号老鼠死亡
// deadMice = [0, 2, 4]
// 结果：0010101 (二进制) = 21 (十进制)
// 说明21号酒桶有毒
```

**数学原理：**
这个问题本质上是用最少的信息量来区分100种可能性。根据信息论，需要的信息量为：
```
log₂(100) ≈ 6.64 bits
```
因此需要至少7个二进制位，即7只老鼠。

**答案：最少需要7只老鼠。**


## **第三轮面试**

### 1、自我介绍一下。

### 2、简单介绍毕设。

### 3、了解Solidity语言多长时间。

### 4、之前工作遇到的困难。

### 5、为什么毕设要挑一个完全不熟悉的方向。

### 6、问我这段时间求职有拿过其他offer吗。

### 7、问我的期望薪资。

### 8、你对Web3行业发展怎么看。

**答案：**

**Web3行业发展现状与前景：**

**1. 当前发展阶段**
- 处于早期发展阶段，类似2000年初的互联网
- 基础设施逐步完善（Layer2、跨链、存储等）
- 用户体验仍有较大改进空间
- 监管政策逐步明朗化

**2. 核心价值主张**
```
Web1: 只读（Read）
Web2: 读写（Read + Write）
Web3: 读写拥有（Read + Write + Own）
```

**技术发展趋势：**
- **可扩展性**：Layer2解决方案成熟（Arbitrum、Optimism、Polygon）
- **互操作性**：跨链协议发展（Cosmos、Polkadot）
- **用户体验**：钱包集成、Gas费优化、账户抽象
- **隐私保护**：零知识证明技术应用

**3. 应用场景拓展**

**金融领域（DeFi）：**
- 去中心化交易所（DEX）
- 借贷协议、流动性挖矿
- 合成资产、衍生品交易

**数字资产（NFT）：**
- 数字艺术品、游戏道具
- 身份认证、会员权益
- 实物资产代币化

**去中心化治理（DAO）：**
- 社区自治组织
- 投资决策民主化
- 协议参数治理

**4. 面临的挑战**

**技术挑战：**
- 区块链三难问题（安全性、去中心化、可扩展性）
- 用户体验复杂度高
- 能耗和环保问题

**监管挑战：**
- 各国监管政策不统一
- 合规成本较高
- 创新与监管平衡

**5. 个人看法**

**短期（1-2年）：**
- 基础设施继续完善
- 机构采用率提升
- 监管框架逐步建立

**中期（3-5年）：**
- 主流应用场景成熟
- 用户体验显著改善
- 与传统互联网深度融合

**长期（5-10年）：**
- 成为互联网基础设施的重要组成
- 数字经济新范式确立
- 价值互联网真正实现

**投资建议：**
- 关注基础设施项目（公链、Layer2、存储）
- 重视实际应用价值而非投机炒作
- 长期看好但需要耐心等待技术成熟

**总结：**
Web3代表了互联网发展的下一个阶段，虽然目前还面临诸多挑战，但其去中心化、用户拥有数据和价值的理念具有革命性意义。作为技术从业者，应该积极参与这个历史性变革，同时保持理性和耐心。


## **Offer后沟通**

### 1、聊了聊职业规划，和行业的一些政策。

### 2、问我职业发展主要考虑什么。

### 3、对薪资的看法。

### 4、谈对Web3、尤其公链的理解。

### 5、问我C++拷贝构造函数与go语言。

### 6、问我想参与上层应用开发，还是底层区块链开发。

### 7、介绍分布式办公模式。